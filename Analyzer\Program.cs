﻿using System;
using System.Reflection;
using System.Linq;
using System.IO;

try
{
    string assemblyPath = "../KillingFloor2_External.dll";
    if (!File.Exists(assemblyPath))
    {
        Console.WriteLine($"Assembly not found: {assemblyPath}");
        return;
    }

    Assembly assembly = Assembly.LoadFrom(assemblyPath);
    Console.WriteLine($"Assembly: {assembly.FullName}");
    Console.WriteLine("=" + new string('=', 50));

    // Get all types
    Type[] types = assembly.GetTypes();
    Console.WriteLine($"Total Types: {types.Length}");
    Console.WriteLine();

    foreach (Type type in types.Take(20)) // Limit to first 20 types
    {
        Console.WriteLine($"Type: {type.FullName}");
        Console.WriteLine($"  Base Type: {type.BaseType?.Name}");

        // Get public methods
        var methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.Static | BindingFlags.DeclaredOnly);
        if (methods.Length > 0)
        {
            Console.WriteLine("  Methods:");
            foreach (var method in methods.Take(10)) // Limit methods
            {
                Console.WriteLine($"    {method.Name}({string.Join(", ", method.GetParameters().Select(p => p.ParameterType.Name))})");
            }
        }

        // Get public properties
        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance | BindingFlags.Static | BindingFlags.DeclaredOnly);
        if (properties.Length > 0)
        {
            Console.WriteLine("  Properties:");
            foreach (var prop in properties.Take(10)) // Limit properties
            {
                Console.WriteLine($"    {prop.PropertyType.Name} {prop.Name}");
            }
        }

        // Get public fields
        var fields = type.GetFields(BindingFlags.Public | BindingFlags.Instance | BindingFlags.Static | BindingFlags.DeclaredOnly);
        if (fields.Length > 0)
        {
            Console.WriteLine("  Fields:");
            foreach (var field in fields.Take(10)) // Limit fields
            {
                Console.WriteLine($"    {field.FieldType.Name} {field.Name}");
            }
        }

        Console.WriteLine();
    }

    // Look for forms specifically
    Console.WriteLine("FORMS AND UI COMPONENTS:");
    Console.WriteLine("=" + new string('=', 30));
    var formTypes = types.Where(t => t.BaseType?.Name == "Form" || t.Name.Contains("Form") || t.Name.Contains("UI")).ToArray();
    foreach (var formType in formTypes)
    {
        Console.WriteLine($"Form: {formType.Name}");
        var controls = formType.GetFields(BindingFlags.NonPublic | BindingFlags.Instance)
            .Where(f => f.Name.Contains("button") || f.Name.Contains("checkbox") || f.Name.Contains("label") || f.Name.Contains("textbox"))
            .Take(20);

        foreach (var control in controls)
        {
            Console.WriteLine($"  Control: {control.FieldType.Name} {control.Name}");
        }
        Console.WriteLine();
    }

}
catch (Exception ex)
{
    Console.WriteLine($"Error: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
}
