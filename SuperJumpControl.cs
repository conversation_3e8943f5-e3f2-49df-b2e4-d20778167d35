using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MaterialSkin.Controls;
using Memory;
using KillingFloor2_External.Features;

namespace KillingFloor2_External.Controls
{
    /// <summary>
    /// Material Design UI control for Super Jump feature
    /// </summary>
    public partial class SuperJumpControl : UserControl
    {
        private SuperJumpFeature superJumpFeature;
        private Mem memory;

        // UI Components
        private MaterialCard cardContainer;
        private MaterialLabel lblTitle;
        private MaterialSwitch switchEnable;
        private MaterialSlider sliderMultiplier;
        private MaterialLabel lblMultiplier;
        private MaterialButton btnApplyJump;
        private MaterialButton btnRestoreNormal;
        private MaterialLabel lblStatus;
        private MaterialProgressBar progressBar;

        public SuperJumpControl()
        {
            InitializeComponent();
            SetupMaterialDesign();
        }

        public SuperJumpControl(Mem memoryInstance) : this()
        {
            memory = memoryInstance;
            superJumpFeature = new SuperJumpFeature(memory);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Set control properties
            this.Size = new Size(350, 280);
            this.BackColor = Color.FromArgb(55, 71, 79);
            this.Padding = new Padding(10);

            this.ResumeLayout(false);
        }

        private void SetupMaterialDesign()
        {
            // Main container card
            cardContainer = new MaterialCard
            {
                Location = new Point(5, 5),
                Size = new Size(340, 270),
                BackColor = Color.FromArgb(66, 66, 66),
                Padding = new Padding(15)
            };

            // Title label
            lblTitle = new MaterialLabel
            {
                Text = "Super Jump",
                Location = new Point(15, 15),
                Size = new Size(120, 30),
                Font = new Font("Roboto", 14, FontStyle.Bold),
                ForeColor = Color.White
            };

            // Enable/Disable switch
            switchEnable = new MaterialSwitch
            {
                Location = new Point(15, 50),
                Size = new Size(150, 30),
                Text = "Enable Super Jump",
                UseAccentColor = true
            };
            switchEnable.CheckedChanged += SwitchEnable_CheckedChanged;

            // Jump multiplier slider
            MaterialLabel lblMultiplierTitle = new MaterialLabel
            {
                Text = "Jump Height Multiplier:",
                Location = new Point(15, 90),
                Size = new Size(180, 20),
                Font = new Font("Roboto", 10),
                ForeColor = Color.LightGray
            };

            sliderMultiplier = new MaterialSlider
            {
                Location = new Point(15, 115),
                Size = new Size(250, 30),
                RangeMin = 1,
                RangeMax = 10,
                Value = 2,
                ShowValue = false,
                UseAccentColor = true
            };
            sliderMultiplier.onValueChanged += SliderMultiplier_ValueChanged;

            // Multiplier value label
            lblMultiplier = new MaterialLabel
            {
                Text = "2.0x",
                Location = new Point(270, 115),
                Size = new Size(50, 30),
                Font = new Font("Roboto", 10, FontStyle.Bold),
                ForeColor = Color.Cyan,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Apply jump button
            btnApplyJump = new MaterialButton
            {
                Text = "APPLY SUPER JUMP",
                Location = new Point(15, 155),
                Size = new Size(150, 35),
                Type = MaterialButton.MaterialButtonType.Contained,
                UseAccentColor = true,
                Enabled = false
            };
            btnApplyJump.Click += BtnApplyJump_Click;

            // Restore normal button
            btnRestoreNormal = new MaterialButton
            {
                Text = "RESTORE NORMAL",
                Location = new Point(175, 155),
                Size = new Size(150, 35),
                Type = MaterialButton.MaterialButtonType.Outlined,
                UseAccentColor = false,
                Enabled = false
            };
            btnRestoreNormal.Click += BtnRestoreNormal_Click;

            // Status label
            lblStatus = new MaterialLabel
            {
                Text = "Status: Disabled",
                Location = new Point(15, 200),
                Size = new Size(200, 20),
                Font = new Font("Roboto", 9),
                ForeColor = Color.Orange
            };

            // Progress bar for operations
            progressBar = new MaterialProgressBar
            {
                Location = new Point(15, 225),
                Size = new Size(310, 5),
                Style = ProgressBarStyle.Marquee,
                Visible = false
            };

            // Add all controls to the card
            cardContainer.Controls.AddRange(new Control[]
            {
                lblTitle,
                switchEnable,
                lblMultiplierTitle,
                sliderMultiplier,
                lblMultiplier,
                btnApplyJump,
                btnRestoreNormal,
                lblStatus,
                progressBar
            });

            // Add card to main control
            this.Controls.Add(cardContainer);
        }

        private async void SwitchEnable_CheckedChanged(object sender, EventArgs e)
        {
            if (superJumpFeature == null) return;

            SetUIBusy(true);

            try
            {
                if (switchEnable.Checked)
                {
                    bool success = await superJumpFeature.EnableAsync();
                    if (success)
                    {
                        lblStatus.Text = "Status: Enabled";
                        lblStatus.ForeColor = Color.LimeGreen;
                        btnApplyJump.Enabled = true;
                        btnRestoreNormal.Enabled = true;
                        sliderMultiplier.Enabled = true;
                    }
                    else
                    {
                        switchEnable.Checked = false;
                        lblStatus.Text = "Status: Failed to Enable";
                        lblStatus.ForeColor = Color.Red;
                        ShowErrorMessage("Failed to enable Super Jump. Make sure KF2 is running.");
                    }
                }
                else
                {
                    bool success = await superJumpFeature.DisableAsync();
                    lblStatus.Text = success ? "Status: Disabled" : "Status: Error Disabling";
                    lblStatus.ForeColor = success ? Color.Orange : Color.Red;
                    btnApplyJump.Enabled = false;
                    btnRestoreNormal.Enabled = false;
                    sliderMultiplier.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"Error: {ex.Message}");
                switchEnable.Checked = false;
            }
            finally
            {
                SetUIBusy(false);
            }
        }

        private void SliderMultiplier_ValueChanged(MaterialSlider sender, int newValue)
        {
            float multiplier = newValue;
            lblMultiplier.Text = $"{multiplier:F1}x";
            
            if (superJumpFeature != null)
            {
                superJumpFeature.JumpMultiplier = multiplier;
            }

            // Color coding for multiplier intensity
            if (multiplier <= 2.0f)
                lblMultiplier.ForeColor = Color.LimeGreen;
            else if (multiplier <= 5.0f)
                lblMultiplier.ForeColor = Color.Yellow;
            else
                lblMultiplier.ForeColor = Color.Red;
        }

        private async void BtnApplyJump_Click(object sender, EventArgs e)
        {
            if (superJumpFeature == null) return;

            SetUIBusy(true);
            try
            {
                bool success = await superJumpFeature.ApplySuperJumpAsync();
                if (success)
                {
                    lblStatus.Text = $"Status: Super Jump Active ({superJumpFeature.JumpMultiplier:F1}x)";
                    lblStatus.ForeColor = Color.Cyan;
                }
                else
                {
                    ShowErrorMessage("Failed to apply super jump effect.");
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"Error applying super jump: {ex.Message}");
            }
            finally
            {
                SetUIBusy(false);
            }
        }

        private async void BtnRestoreNormal_Click(object sender, EventArgs e)
        {
            if (superJumpFeature == null) return;

            SetUIBusy(true);
            try
            {
                bool success = await superJumpFeature.RestoreNormalJumpAsync();
                if (success)
                {
                    lblStatus.Text = "Status: Normal Jump Restored";
                    lblStatus.ForeColor = Color.LimeGreen;
                }
                else
                {
                    ShowErrorMessage("Failed to restore normal jump.");
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"Error restoring normal jump: {ex.Message}");
            }
            finally
            {
                SetUIBusy(false);
            }
        }

        private void SetUIBusy(bool busy)
        {
            progressBar.Visible = busy;
            switchEnable.Enabled = !busy;
            btnApplyJump.Enabled = !busy && superJumpFeature?.IsEnabled == true;
            btnRestoreNormal.Enabled = !busy && superJumpFeature?.IsEnabled == true;
            sliderMultiplier.Enabled = !busy && superJumpFeature?.IsEnabled == true;
        }

        private void ShowErrorMessage(string message)
        {
            MessageBox.Show(message, "Super Jump Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                superJumpFeature?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
