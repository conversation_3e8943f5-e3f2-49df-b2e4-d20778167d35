using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using MaterialSkin;
using MaterialSkin.Controls;
using Memory;
using KillingFloor2_External.Integration;
using KillingFloor2_External.Configuration;

namespace KillingFloor2_External
{
    /// <summary>
    /// Example of how to integrate Super Jump into the main KF2 tool form
    /// This demonstrates the integration process for the existing application
    /// </summary>
    public partial class MainForm : MaterialForm
    {
        private Mem memory;
        private SuperJumpIntegration superJumpIntegration;
        private MaterialTabControl tabControl;
        private MaterialLabel statusLabel;

        // Add this to your existing MainForm constructor or Load event
        private void InitializeSuperJumpFeature()
        {
            try
            {
                // Initialize memory instance (this should already exist in your app)
                memory = new Mem();
                
                // Try to attach to KF2 process
                if (!AttachToKF2Process())
                {
                    ShowStatusMessage("KF2 process not found. Super Jump will be available when KF2 is running.", false);
                    return;
                }

                // Create super jump integration
                superJumpIntegration = new SuperJumpIntegration(memory, this);

                // Add to your existing tab control (assuming you have one)
                if (tabControl != null)
                {
                    tabControl.AddSuperJumpTab(superJumpIntegration);
                }
                else
                {
                    // Or add to a panel if you don't use tabs
                    AddSuperJumpToMainPanel();
                }

                ShowStatusMessage("Super Jump feature initialized successfully!", true);
                Console.WriteLine("[MainForm] Super Jump feature added successfully");
            }
            catch (Exception ex)
            {
                ShowStatusMessage($"Error initializing Super Jump: {ex.Message}", false);
                Console.WriteLine($"[MainForm] Error initializing Super Jump: {ex.Message}");
            }
        }

        private bool AttachToKF2Process()
        {
            try
            {
                // Try common KF2 process names
                string[] processNames = { "KFGame", "KillingFloor2", "KF2" };
                
                foreach (string processName in processNames)
                {
                    if (memory.OpenProcess(processName))
                    {
                        Console.WriteLine($"[MainForm] Attached to KF2 process: {processName}");
                        return true;
                    }
                }

                // Try by window title
                var processes = System.Diagnostics.Process.GetProcesses();
                foreach (var process in processes)
                {
                    if (process.MainWindowTitle.Contains("Killing Floor 2") && 
                        memory.OpenProcess(process.Id))
                    {
                        Console.WriteLine($"[MainForm] Attached to KF2 process by window title: {process.ProcessName}");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[MainForm] Error attaching to KF2 process: {ex.Message}");
                return false;
            }
        }

        private void AddSuperJumpToMainPanel()
        {
            // If you have a main features panel, add super jump there
            Panel featuresPanel = this.Controls.Find("featuresPanel", true).FirstOrDefault() as Panel;
            
            if (featuresPanel != null)
            {
                featuresPanel.AddSuperJumpToPanel(superJumpIntegration);
            }
            else
            {
                // Create a new panel if none exists
                CreateFeaturesPanel();
            }
        }

        private void CreateFeaturesPanel()
        {
            var featuresPanel = new Panel
            {
                Name = "featuresPanel",
                Location = new System.Drawing.Point(20, 80),
                Size = new System.Drawing.Size(400, 600),
                AutoScroll = true,
                BackColor = System.Drawing.Color.FromArgb(55, 71, 79)
            };

            this.Controls.Add(featuresPanel);
            featuresPanel.AddSuperJumpToPanel(superJumpIntegration);
        }

        private void ShowStatusMessage(string message, bool isSuccess)
        {
            if (statusLabel != null)
            {
                statusLabel.Text = message;
                statusLabel.ForeColor = isSuccess ? 
                    System.Drawing.Color.LimeGreen : 
                    System.Drawing.Color.Orange;
            }

            // Also show in console
            Console.WriteLine($"[MainForm] {message}");
        }

        // Add this to your existing form closing event
        private async void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // Cleanup super jump integration
                if (superJumpIntegration != null)
                {
                    await superJumpIntegration.CleanupAsync();
                }

                // Close memory handle
                memory?.CloseProcess();

                Console.WriteLine("[MainForm] Application cleanup completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[MainForm] Error during cleanup: {ex.Message}");
            }
        }

        // Example of how to add menu items for super jump
        private void AddSuperJumpMenuItems()
        {
            // If you have a main menu, add super jump options
            var mainMenu = this.MainMenuStrip;
            if (mainMenu != null)
            {
                var featuresMenu = new ToolStripMenuItem("Features");
                
                var superJumpMenu = new ToolStripMenuItem("Super Jump");
                superJumpMenu.DropDownItems.Add(new ToolStripMenuItem("Toggle (F5)", null, async (s, e) => 
                    await superJumpIntegration?.ToggleSuperJumpAsync()));
                superJumpMenu.DropDownItems.Add(new ToolStripMenuItem("Apply Jump (Ctrl+F5)", null, async (s, e) => 
                    await superJumpIntegration?.ApplySuperJumpAsync()));
                superJumpMenu.DropDownItems.Add(new ToolStripSeparator());
                superJumpMenu.DropDownItems.Add(new ToolStripMenuItem("Increase Multiplier", null, async (s, e) => 
                    await superJumpIntegration?.IncreaseMultiplierAsync()));
                superJumpMenu.DropDownItems.Add(new ToolStripMenuItem("Decrease Multiplier", null, async (s, e) => 
                    await superJumpIntegration?.DecreaseMultiplierAsync()));
                
                featuresMenu.DropDownItems.Add(superJumpMenu);
                mainMenu.Items.Add(featuresMenu);
            }
        }

        // Example of status monitoring
        private async void StartStatusMonitoring()
        {
            var timer = new Timer { Interval = 2000 }; // Update every 2 seconds
            timer.Tick += (s, e) => UpdateStatusDisplay();
            timer.Start();
        }

        private void UpdateStatusDisplay()
        {
            if (superJumpIntegration != null && statusLabel != null)
            {
                string status = superJumpIntegration.GetStatusInfo();
                
                // Update status in a thread-safe way
                if (statusLabel.InvokeRequired)
                {
                    statusLabel.Invoke(new Action(() => statusLabel.Text = status));
                }
                else
                {
                    statusLabel.Text = status;
                }
            }
        }

        // Example of how to handle process detection
        private void SetupProcessMonitoring()
        {
            var processTimer = new Timer { Interval = 5000 }; // Check every 5 seconds
            processTimer.Tick += (s, e) =>
            {
                if (memory != null && !memory.theProc.HasExited)
                    return; // Process is still running

                // Try to reattach
                if (AttachToKF2Process())
                {
                    ShowStatusMessage("Reconnected to KF2 process", true);
                }
                else
                {
                    ShowStatusMessage("KF2 process not found", false);
                }
            };
            processTimer.Start();
        }
    }

    /// <summary>
    /// Configuration for the main application
    /// </summary>
    public static class AppConfig
    {
        public static void InitializeConfigurations()
        {
            // Load all feature configurations
            ConfigManager.ReloadAll();
            
            // Create backups
            ConfigManager.CreateBackups();
            
            Console.WriteLine("[AppConfig] Configurations initialized");
        }

        public static void SaveAllConfigurations()
        {
            ConfigManager.SaveAll();
            Console.WriteLine("[AppConfig] All configurations saved");
        }
    }
}
