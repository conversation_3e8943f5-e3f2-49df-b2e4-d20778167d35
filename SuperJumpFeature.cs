using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Memory;
using KillingFloor2_External.Configuration;

namespace KillingFloor2_External.Features
{
    /// <summary>
    /// Super Jump feature implementation for KF2
    /// Provides enhanced jumping capabilities with configurable height multipliers
    /// </summary>
    public class SuperJumpFeature
    {
        private readonly Mem memory;
        private readonly SuperJumpConfig config;
        private bool isEnabled = false;
        private bool isActive = false;
        private float jumpMultiplier = 2.0f;
        private float originalJumpForce = 0f;
        private float originalGravity = 0f;
        private CancellationTokenSource cancellationTokenSource;
        private IntPtr cachedPlayerBaseAddress = IntPtr.Zero;
        private DateTime lastAddressUpdate = DateTime.MinValue;

        // Events
        public event EventHandler<bool> EnabledChanged;
        public event EventHandler<float> MultiplierChanged;
        public event EventHandler<string> StatusChanged;

        // Default values
        private const float DEFAULT_JUMP_FORCE = 420.0f;
        private const float DEFAULT_GRAVITY = 1.0f;
        private const float SUPER_JUMP_FORCE = 1200.0f;
        private const int ADDRESS_CACHE_DURATION_MS = 5000; // Cache address for 5 seconds

        public SuperJumpFeature(Mem memoryInstance, SuperJumpConfig configuration = null)
        {
            memory = memoryInstance ?? throw new ArgumentNullException(nameof(memoryInstance));
            config = configuration ?? ConfigManager.SuperJump;
            jumpMultiplier = config.JumpMultiplier;
        }

        /// <summary>
        /// Gets or sets the jump height multiplier (1.0 = normal, 2.0 = double height, etc.)
        /// </summary>
        public float JumpMultiplier
        {
            get => jumpMultiplier;
            set => jumpMultiplier = Math.Max(0.1f, Math.Min(10.0f, value)); // Clamp between 0.1x and 10x
        }

        /// <summary>
        /// Gets whether the super jump feature is currently enabled
        /// </summary>
        public bool IsEnabled => isEnabled;

        /// <summary>
        /// Enable the super jump feature
        /// </summary>
        public async Task<bool> EnableAsync()
        {
            if (isEnabled) return true;

            try
            {
                // Find the player base address
                var playerBaseAddress = await FindPlayerBaseAddressAsync();
                if (playerBaseAddress == IntPtr.Zero)
                {
                    Console.WriteLine("[SuperJump] Failed to find player base address");
                    return false;
                }

                // Store original jump force for restoration
                originalJumpForce = memory.ReadFloat($"0x{playerBaseAddress.ToInt64() + JUMP_FORCE_OFFSET:X}");
                
                if (originalJumpForce == 0f)
                {
                    originalJumpForce = DEFAULT_JUMP_FORCE;
                }

                isEnabled = true;
                cancellationTokenSource = new CancellationTokenSource();

                // Start the super jump monitoring task
                _ = Task.Run(() => SuperJumpMonitorLoop(cancellationTokenSource.Token));

                Console.WriteLine($"[SuperJump] Enabled with {jumpMultiplier}x multiplier");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJump] Error enabling: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disable the super jump feature and restore original values
        /// </summary>
        public async Task<bool> DisableAsync()
        {
            if (!isEnabled) return true;

            try
            {
                isEnabled = false;
                cancellationTokenSource?.Cancel();

                // Restore original jump force
                await RestoreOriginalValuesAsync();

                Console.WriteLine("[SuperJump] Disabled and original values restored");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJump] Error disabling: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Apply super jump effect immediately (for instant activation)
        /// </summary>
        public async Task<bool> ApplySuperJumpAsync()
        {
            if (!isEnabled) return false;

            try
            {
                var playerBaseAddress = await FindPlayerBaseAddressAsync();
                if (playerBaseAddress == IntPtr.Zero) return false;

                // Calculate enhanced jump force
                float enhancedJumpForce = originalJumpForce * jumpMultiplier;

                // Write enhanced jump force
                string jumpForceAddress = $"0x{playerBaseAddress.ToInt64() + JUMP_FORCE_OFFSET:X}";
                memory.WriteMemory(jumpForceAddress, "float", enhancedJumpForce.ToString());

                // Optionally modify gravity for more dramatic effect
                if (jumpMultiplier > 3.0f)
                {
                    string gravityAddress = $"0x{playerBaseAddress.ToInt64() + GRAVITY_OFFSET:X}";
                    float reducedGravity = DEFAULT_GRAVITY * (1.0f / (jumpMultiplier * 0.3f));
                    memory.WriteMemory(gravityAddress, "float", reducedGravity.ToString());
                }

                isActive = true;
                Console.WriteLine($"[SuperJump] Applied {jumpMultiplier}x jump boost");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJump] Error applying super jump: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Restore normal jump values
        /// </summary>
        public async Task<bool> RestoreNormalJumpAsync()
        {
            try
            {
                var playerBaseAddress = await FindPlayerBaseAddressAsync();
                if (playerBaseAddress == IntPtr.Zero) return false;

                // Restore original jump force
                string jumpForceAddress = $"0x{playerBaseAddress.ToInt64() + JUMP_FORCE_OFFSET:X}";
                memory.WriteMemory(jumpForceAddress, "float", originalJumpForce.ToString());

                // Restore normal gravity
                string gravityAddress = $"0x{playerBaseAddress.ToInt64() + GRAVITY_OFFSET:X}";
                memory.WriteMemory(gravityAddress, "float", DEFAULT_GRAVITY.ToString());

                isActive = false;
                Console.WriteLine("[SuperJump] Restored normal jump values");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJump] Error restoring normal jump: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Find the player base address using pattern scanning
        /// </summary>
        private async Task<IntPtr> FindPlayerBaseAddressAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    // Use AoB scan to find player base pattern
                    var results = memory.AoBScan(PLAYER_BASE_PATTERN, true, true);
                    
                    foreach (var address in results)
                    {
                        // Validate the address by checking if it contains reasonable values
                        var testValue = memory.ReadFloat($"0x{address + JUMP_FORCE_OFFSET:X}");
                        if (testValue > 100f && testValue < 2000f) // Reasonable jump force range
                        {
                            return new IntPtr(address);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[SuperJump] Pattern scan error: {ex.Message}");
                }

                return IntPtr.Zero;
            });
        }

        /// <summary>
        /// Background monitoring loop for super jump feature
        /// </summary>
        private async Task SuperJumpMonitorLoop(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && isEnabled)
            {
                try
                {
                    // Monitor for jump input or conditions
                    // This could be enhanced to detect jump key presses or game state changes
                    
                    await Task.Delay(50, cancellationToken); // 20 FPS monitoring
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[SuperJump] Monitor loop error: {ex.Message}");
                    await Task.Delay(1000, cancellationToken);
                }
            }
        }

        /// <summary>
        /// Restore all original values when disabling
        /// </summary>
        private async Task RestoreOriginalValuesAsync()
        {
            await RestoreNormalJumpAsync();
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (isEnabled)
            {
                _ = DisableAsync();
            }
            cancellationTokenSource?.Dispose();
        }
    }
}
