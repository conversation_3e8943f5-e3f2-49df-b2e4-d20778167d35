using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using Memory;
using KillingFloor2_External.Features;
using KillingFloor2_External.Controls;
using KillingFloor2_External.Configuration;
using KillingFloor2_External.Input;

namespace KillingFloor2_External.Integration
{
    /// <summary>
    /// Integration example for adding Super Jump feature to the main KF2 tool
    /// This shows how to integrate the super jump feature into the existing application
    /// </summary>
    public class SuperJumpIntegration
    {
        private readonly Mem memory;
        private readonly Form mainForm;
        private SuperJumpFeature superJumpFeature;
        private SuperJumpControl superJumpControl;
        private HotkeyManager hotkeyManager;
        private SuperJumpConfig config;

        // Hotkey IDs for management
        private int toggleHotkeyId = -1;
        private int applyHotkeyId = -1;
        private int increaseHotkeyId = -1;
        private int decreaseHotkeyId = -1;

        public SuperJumpIntegration(Mem memoryInstance, Form parentForm)
        {
            memory = memoryInstance ?? throw new ArgumentNullException(nameof(memoryInstance));
            mainForm = parentForm ?? throw new ArgumentNullException(nameof(parentForm));
            
            Initialize();
        }

        private void Initialize()
        {
            // Load configuration
            config = ConfigManager.SuperJump;

            // Create the super jump feature
            superJumpFeature = new SuperJumpFeature(memory, config);

            // Create the UI control
            superJumpControl = new SuperJumpControl(memory);

            // Setup hotkey manager
            hotkeyManager = new HotkeyManager(mainForm);

            // Setup event handlers
            SetupEventHandlers();

            // Register hotkeys
            RegisterHotkeys();

            Console.WriteLine("[SuperJump] Integration initialized successfully");
        }

        private void SetupEventHandlers()
        {
            // Super jump feature events
            superJumpFeature.EnabledChanged += OnSuperJumpEnabledChanged;
            superJumpFeature.MultiplierChanged += OnSuperJumpMultiplierChanged;
            superJumpFeature.StatusChanged += OnSuperJumpStatusChanged;

            // Application events
            mainForm.FormClosing += OnMainFormClosing;
        }

        private void RegisterHotkeys()
        {
            try
            {
                // Register toggle hotkey (default: F5)
                toggleHotkeyId = hotkeyManager.RegisterHotkey(
                    config.Hotkey, 
                    async () => await ToggleSuperJumpAsync()
                );

                // Register apply hotkey (Ctrl+F5)
                applyHotkeyId = hotkeyManager.RegisterHotkey(
                    CommonHotkeys.SuperJumpToggle,
                    async () => await ApplySuperJumpAsync()
                );

                // Register increase multiplier hotkey (Ctrl+Shift+Plus)
                increaseHotkeyId = hotkeyManager.RegisterHotkey(
                    CommonHotkeys.SuperJumpIncrease,
                    async () => await IncreaseMultiplierAsync()
                );

                // Register decrease multiplier hotkey (Ctrl+Shift+Minus)
                decreaseHotkeyId = hotkeyManager.RegisterHotkey(
                    CommonHotkeys.SuperJumpDecrease,
                    async () => await DecreaseMultiplierAsync()
                );

                Console.WriteLine($"[SuperJump] Hotkeys registered - Toggle: {config.Hotkey}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJump] Error registering hotkeys: {ex.Message}");
            }
        }

        /// <summary>
        /// Add the super jump control to a parent container (e.g., TabPage, Panel)
        /// </summary>
        public void AddToContainer(Control container, int x = 10, int y = 10)
        {
            if (container == null) return;

            superJumpControl.Location = new System.Drawing.Point(x, y);
            container.Controls.Add(superJumpControl);
        }

        /// <summary>
        /// Toggle super jump on/off
        /// </summary>
        public async Task<bool> ToggleSuperJumpAsync()
        {
            try
            {
                if (superJumpFeature.IsEnabled)
                {
                    return await superJumpFeature.DisableAsync();
                }
                else
                {
                    return await superJumpFeature.EnableAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJump] Error toggling: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Apply super jump effect
        /// </summary>
        public async Task<bool> ApplySuperJumpAsync()
        {
            if (!superJumpFeature.IsEnabled)
            {
                // Auto-enable if not enabled
                bool enabled = await superJumpFeature.EnableAsync();
                if (!enabled) return false;
            }

            return await superJumpFeature.ApplySuperJumpAsync();
        }

        /// <summary>
        /// Increase jump multiplier
        /// </summary>
        public async Task IncreaseMultiplierAsync()
        {
            float currentMultiplier = superJumpFeature.JumpMultiplier;
            float newMultiplier = Math.Min(config.MaxMultiplier, currentMultiplier + 0.5f);
            
            superJumpFeature.JumpMultiplier = newMultiplier;
            config.JumpMultiplier = newMultiplier;
            config.Save();

            Console.WriteLine($"[SuperJump] Multiplier increased to {newMultiplier:F1}x");

            // Apply if currently active
            if (superJumpFeature.IsEnabled)
            {
                await superJumpFeature.ApplySuperJumpAsync();
            }
        }

        /// <summary>
        /// Decrease jump multiplier
        /// </summary>
        public async Task DecreaseMultiplierAsync()
        {
            float currentMultiplier = superJumpFeature.JumpMultiplier;
            float newMultiplier = Math.Max(config.MinMultiplier, currentMultiplier - 0.5f);
            
            superJumpFeature.JumpMultiplier = newMultiplier;
            config.JumpMultiplier = newMultiplier;
            config.Save();

            Console.WriteLine($"[SuperJump] Multiplier decreased to {newMultiplier:F1}x");

            // Apply if currently active
            if (superJumpFeature.IsEnabled)
            {
                await superJumpFeature.ApplySuperJumpAsync();
            }
        }

        /// <summary>
        /// Get current status information
        /// </summary>
        public string GetStatusInfo()
        {
            return $"SuperJump: {(superJumpFeature.IsEnabled ? "Enabled" : "Disabled")} " +
                   $"({superJumpFeature.JumpMultiplier:F1}x)";
        }

        // Event handlers
        private void OnSuperJumpEnabledChanged(object sender, bool enabled)
        {
            config.Enabled = enabled;
            config.Save();
            Console.WriteLine($"[SuperJump] Status changed: {(enabled ? "Enabled" : "Disabled")}");
        }

        private void OnSuperJumpMultiplierChanged(object sender, float multiplier)
        {
            config.JumpMultiplier = multiplier;
            config.Save();
            Console.WriteLine($"[SuperJump] Multiplier changed: {multiplier:F1}x");
        }

        private void OnSuperJumpStatusChanged(object sender, string status)
        {
            Console.WriteLine($"[SuperJump] {status}");
        }

        private async void OnMainFormClosing(object sender, FormClosingEventArgs e)
        {
            await CleanupAsync();
        }

        /// <summary>
        /// Cleanup resources
        /// </summary>
        public async Task CleanupAsync()
        {
            try
            {
                // Disable super jump and restore normal values
                if (superJumpFeature?.IsEnabled == true)
                {
                    await superJumpFeature.DisableAsync();
                }

                // Unregister hotkeys
                hotkeyManager?.UnregisterHotkey(toggleHotkeyId);
                hotkeyManager?.UnregisterHotkey(applyHotkeyId);
                hotkeyManager?.UnregisterHotkey(increaseHotkeyId);
                hotkeyManager?.UnregisterHotkey(decreaseHotkeyId);

                // Dispose resources
                superJumpFeature?.Dispose();
                hotkeyManager?.Dispose();

                // Save final configuration
                config?.Save();

                Console.WriteLine("[SuperJump] Cleanup completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJump] Error during cleanup: {ex.Message}");
            }
        }

        /// <summary>
        /// Update memory offsets for new game version
        /// </summary>
        public void UpdateMemoryOffsets(SuperJumpConfig.MemoryOffsets newOffsets)
        {
            config.UpdateOffsetsForVersion("current", newOffsets);
            Console.WriteLine("[SuperJump] Memory offsets updated");
        }

        /// <summary>
        /// Enable safe mode (limits multiplier values)
        /// </summary>
        public void EnableSafeMode(bool enable = true)
        {
            config.SafeMode = enable;
            config.Save();
            
            if (enable && superJumpFeature.JumpMultiplier > 5.0f)
            {
                superJumpFeature.JumpMultiplier = 5.0f;
            }
            
            Console.WriteLine($"[SuperJump] Safe mode {(enable ? "enabled" : "disabled")}");
        }
    }

    /// <summary>
    /// Extension methods for easier integration
    /// </summary>
    public static class SuperJumpExtensions
    {
        /// <summary>
        /// Add super jump tab to a TabControl
        /// </summary>
        public static void AddSuperJumpTab(this TabControl tabControl, SuperJumpIntegration integration)
        {
            var tabPage = new TabPage("Super Jump")
            {
                BackColor = System.Drawing.Color.FromArgb(55, 71, 79),
                Padding = new Padding(10)
            };

            integration.AddToContainer(tabPage);
            tabControl.TabPages.Add(tabPage);
        }

        /// <summary>
        /// Add super jump to a features panel
        /// </summary>
        public static void AddSuperJumpToPanel(this Panel panel, SuperJumpIntegration integration, int row = 0)
        {
            int yPosition = row * 290; // Assuming each feature takes ~290px height
            integration.AddToContainer(panel, 10, yPosition);
        }
    }
}
