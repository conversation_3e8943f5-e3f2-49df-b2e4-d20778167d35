using System;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace KillingFloor2_External.Configuration
{
    /// <summary>
    /// Configuration class for Super Jump feature settings
    /// </summary>
    public class SuperJumpConfig
    {
        private const string CONFIG_FILE = "superjump_config.json";

        [JsonPropertyName("enabled")]
        public bool Enabled { get; set; } = false;

        [JsonPropertyName("jumpMultiplier")]
        public float JumpMultiplier { get; set; } = 2.0f;

        [JsonPropertyName("autoApply")]
        public bool AutoApply { get; set; } = false;

        [JsonPropertyName("hotkey")]
        public string Hotkey { get; set; } = "F5";

        [JsonPropertyName("maxMultiplier")]
        public float MaxMultiplier { get; set; } = 10.0f;

        [JsonPropertyName("minMultiplier")]
        public float MinMultiplier { get; set; } = 0.1f;

        [JsonPropertyName("safeMode")]
        public bool SafeMode { get; set; } = true;

        [JsonPropertyName("restoreOnDisable")]
        public bool RestoreOnDisable { get; set; } = true;

        [JsonPropertyName("memoryOffsets")]
        public MemoryOffsets Offsets { get; set; } = new MemoryOffsets();

        /// <summary>
        /// Memory offsets for different game versions
        /// </summary>
        public class MemoryOffsets
        {
            [JsonPropertyName("jumpForceOffset")]
            public int JumpForceOffset { get; set; } = 0x2A4;

            [JsonPropertyName("gravityOffset")]
            public int GravityOffset { get; set; } = 0x2A8;

            [JsonPropertyName("velocityZOffset")]
            public int VelocityZOffset { get; set; } = 0x14C;

            [JsonPropertyName("playerBasePattern")]
            public string PlayerBasePattern { get; set; } = "48 8B 05 ? ? ? ? 48 8B 88 ? ? ? ? 48 85 C9";
        }

        /// <summary>
        /// Load configuration from file
        /// </summary>
        public static SuperJumpConfig Load()
        {
            try
            {
                if (File.Exists(CONFIG_FILE))
                {
                    string json = File.ReadAllText(CONFIG_FILE);
                    var config = JsonSerializer.Deserialize<SuperJumpConfig>(json);
                    
                    // Validate loaded values
                    config.JumpMultiplier = Math.Max(config.MinMultiplier, 
                                           Math.Min(config.MaxMultiplier, config.JumpMultiplier));
                    
                    return config;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJumpConfig] Error loading config: {ex.Message}");
            }

            return new SuperJumpConfig();
        }

        /// <summary>
        /// Save configuration to file
        /// </summary>
        public void Save()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                string json = JsonSerializer.Serialize(this, options);
                File.WriteAllText(CONFIG_FILE, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJumpConfig] Error saving config: {ex.Message}");
            }
        }

        /// <summary>
        /// Reset to default values
        /// </summary>
        public void ResetToDefaults()
        {
            Enabled = false;
            JumpMultiplier = 2.0f;
            AutoApply = false;
            Hotkey = "F5";
            MaxMultiplier = 10.0f;
            MinMultiplier = 0.1f;
            SafeMode = true;
            RestoreOnDisable = true;
            Offsets = new MemoryOffsets();
        }

        /// <summary>
        /// Validate configuration values
        /// </summary>
        public bool Validate()
        {
            if (JumpMultiplier < MinMultiplier || JumpMultiplier > MaxMultiplier)
                return false;

            if (MaxMultiplier <= MinMultiplier)
                return false;

            if (string.IsNullOrEmpty(Offsets.PlayerBasePattern))
                return false;

            return true;
        }

        /// <summary>
        /// Get a safe jump multiplier value
        /// </summary>
        public float GetSafeJumpMultiplier()
        {
            if (SafeMode)
            {
                // In safe mode, limit to reasonable values
                return Math.Max(0.5f, Math.Min(5.0f, JumpMultiplier));
            }
            
            return Math.Max(MinMultiplier, Math.Min(MaxMultiplier, JumpMultiplier));
        }

        /// <summary>
        /// Create a backup of current configuration
        /// </summary>
        public void CreateBackup()
        {
            try
            {
                string backupFile = $"{CONFIG_FILE}.backup.{DateTime.Now:yyyyMMdd_HHmmss}";
                if (File.Exists(CONFIG_FILE))
                {
                    File.Copy(CONFIG_FILE, backupFile);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SuperJumpConfig] Error creating backup: {ex.Message}");
            }
        }

        /// <summary>
        /// Update memory offsets for a specific game version
        /// </summary>
        public void UpdateOffsetsForVersion(string gameVersion, MemoryOffsets newOffsets)
        {
            // This could be expanded to support multiple game versions
            Offsets = newOffsets;
            Save();
        }

        /// <summary>
        /// Get configuration summary for logging
        /// </summary>
        public string GetSummary()
        {
            return $"SuperJump Config - Enabled: {Enabled}, Multiplier: {JumpMultiplier:F1}x, " +
                   $"SafeMode: {SafeMode}, Hotkey: {Hotkey}";
        }
    }

    /// <summary>
    /// Configuration manager for handling multiple feature configs
    /// </summary>
    public static class ConfigManager
    {
        private static SuperJumpConfig _superJumpConfig;

        public static SuperJumpConfig SuperJump
        {
            get
            {
                if (_superJumpConfig == null)
                {
                    _superJumpConfig = SuperJumpConfig.Load();
                }
                return _superJumpConfig;
            }
        }

        /// <summary>
        /// Save all configurations
        /// </summary>
        public static void SaveAll()
        {
            _superJumpConfig?.Save();
        }

        /// <summary>
        /// Reload all configurations from disk
        /// </summary>
        public static void ReloadAll()
        {
            _superJumpConfig = SuperJumpConfig.Load();
        }

        /// <summary>
        /// Create backups of all configurations
        /// </summary>
        public static void CreateBackups()
        {
            _superJumpConfig?.CreateBackup();
        }
    }
}
