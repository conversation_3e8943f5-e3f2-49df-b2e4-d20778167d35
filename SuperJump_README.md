# Super Jump Feature for KF2 Tool

## Overview
The Super Jump feature provides enhanced jumping capabilities for Killing Floor 2, allowing players to jump higher than normal with configurable multipliers.

## Features
- **Configurable Jump Height**: Adjust jump multiplier from 0.1x to 10x
- **Global Hotkeys**: Control super jump with customizable keyboard shortcuts
- **Safe Mode**: Limits multiplier values to prevent game crashes
- **Auto-Restore**: Automatically restores normal jump values when disabled
- **Material Design UI**: Modern, intuitive user interface
- **Configuration Persistence**: Settings are saved and restored between sessions

## Installation

### 1. Add Files to Your Project
Copy the following files to your KF2 tool project:
- `SuperJumpFeature.cs` - Core super jump functionality
- `SuperJumpControl.cs` - UI control for the feature
- `SuperJumpConfig.cs` - Configuration management
- `HotkeyManager.cs` - Global hotkey support
- `SuperJumpIntegration.cs` - Integration helper
- `MainFormIntegrationExample.cs` - Integration example

### 2. Add Dependencies
Ensure your project has these NuGet packages:
```xml
<PackageReference Include="Memory" Version="1.2.26" />
<PackageReference Include="MaterialSkin.2.RTL" Version="*******" />
```

### 3. Integrate into Main Form
```csharp
// In your main form constructor or Load event
private void InitializeSuperJump()
{
    var superJumpIntegration = new SuperJumpIntegration(memory, this);
    
    // Add to tab control
    tabControl.AddSuperJumpTab(superJumpIntegration);
    
    // Or add to panel
    featuresPanel.AddSuperJumpToPanel(superJumpIntegration);
}
```

## Usage

### UI Controls
1. **Enable Switch**: Toggle super jump on/off
2. **Multiplier Slider**: Adjust jump height (1.0x = normal, 10.0x = maximum)
3. **Apply Button**: Apply super jump effect immediately
4. **Restore Button**: Restore normal jump values

### Default Hotkeys
- **F5**: Toggle super jump on/off
- **Ctrl+F5**: Apply super jump effect
- **Ctrl+Shift+Plus**: Increase multiplier by 0.5x
- **Ctrl+Shift+Minus**: Decrease multiplier by 0.5x
- **Ctrl+R**: Restore normal jump values

### Configuration
Settings are automatically saved to `superjump_config.json`:
```json
{
  "enabled": false,
  "jumpMultiplier": 2.0,
  "autoApply": false,
  "hotkey": "F5",
  "maxMultiplier": 10.0,
  "minMultiplier": 0.1,
  "safeMode": true,
  "restoreOnDisable": true
}
```

## Memory Offsets

The feature uses pattern scanning to find memory addresses dynamically. Default offsets:
- **Jump Force Offset**: `0x2A4`
- **Gravity Offset**: `0x2A8`
- **Velocity Z Offset**: `0x14C`
- **Player Base Pattern**: `"48 8B 05 ? ? ? ? 48 8B 88 ? ? ? ? 48 85 C9"`

### Updating Offsets
If game updates break the feature, update offsets in configuration:
```csharp
var newOffsets = new SuperJumpConfig.MemoryOffsets
{
    JumpForceOffset = 0x2B0,  // New offset
    GravityOffset = 0x2B4,
    VelocityZOffset = 0x150,
    PlayerBasePattern = "48 8B 05 ? ? ? ? 48 8B 90 ? ? ? ? 48 85 D2"
};

superJumpIntegration.UpdateMemoryOffsets(newOffsets);
```

## Safety Features

### Safe Mode
When enabled (default), limits multiplier to 5.0x to prevent:
- Game crashes from extreme values
- Detection by anti-cheat systems
- Unintended gameplay disruption

### Auto-Restore
Automatically restores original jump values when:
- Feature is disabled
- Application is closed
- Game process is lost

### Error Handling
- Graceful handling of memory access errors
- Automatic retry on temporary failures
- User-friendly error messages

## Troubleshooting

### Common Issues

**"Failed to find player base address"**
- Ensure KF2 is running and in a game
- Try updating memory offsets for current game version
- Check if game is using anti-cheat protection

**"Super jump not working"**
- Verify KF2 process is attached
- Check if you're in single-player mode (required for some versions)
- Ensure sufficient permissions (run as administrator)

**"Hotkeys not responding"**
- Check for conflicting hotkeys with other applications
- Verify hotkey registration in Windows
- Try different key combinations

### Debug Information
Enable console output to see detailed information:
```csharp
Console.WriteLine("[SuperJump] Debug info enabled");
```

## Advanced Usage

### Custom Hotkeys
```csharp
// Register custom hotkey
hotkeyManager.RegisterHotkey("Alt+J", async () => 
{
    await superJumpFeature.ApplySuperJumpAsync();
});
```

### Event Handling
```csharp
superJumpFeature.EnabledChanged += (sender, enabled) => 
{
    Console.WriteLine($"Super jump {(enabled ? "enabled" : "disabled")}");
};

superJumpFeature.MultiplierChanged += (sender, multiplier) => 
{
    Console.WriteLine($"Jump multiplier: {multiplier:F1}x");
};
```

### Programmatic Control
```csharp
// Enable with specific multiplier
await superJumpFeature.EnableAsync();
superJumpFeature.JumpMultiplier = 3.5f;
await superJumpFeature.ApplySuperJumpAsync();

// Restore and disable
await superJumpFeature.RestoreNormalJumpAsync();
await superJumpFeature.DisableAsync();
```

## Security Considerations

⚠️ **Important Warnings:**
- Use only in single-player or private servers
- May trigger anti-cheat detection in online play
- Can result in permanent game bans
- Use at your own risk

### Best Practices
1. Enable Safe Mode for casual use
2. Use reasonable multiplier values (2.0x - 3.0x)
3. Avoid using in competitive gameplay
4. Keep backups of original game files
5. Test in offline mode first

## Support

For issues or questions:
1. Check the troubleshooting section
2. Verify game version compatibility
3. Update memory offsets if needed
4. Report bugs with detailed error messages

## License
This code is provided for educational purposes only. Use responsibly and in accordance with game terms of service.
