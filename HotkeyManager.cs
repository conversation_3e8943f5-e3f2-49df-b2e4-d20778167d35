using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Threading.Tasks;

namespace KillingFloor2_External.Input
{
    /// <summary>
    /// Global hotkey manager for KF2 cheat features
    /// </summary>
    public class HotkeyManager : IDisposable
    {
        private const int WM_HOTKEY = 0x0312;
        private const int MOD_ALT = 0x0001;
        private const int MOD_CONTROL = 0x0002;
        private const int MOD_SHIFT = 0x0004;
        private const int MOD_WIN = 0x0008;

        [DllImport("user32.dll")]
        private static extern bool RegisterHotKey(IntPtr hWnd, int id, int fsModifiers, int vk);

        [DllImport("user32.dll")]
        private static extern bool UnregisterHotKey(IntPtr hWnd, int id);

        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        private readonly Dictionary<int, HotkeyAction> registeredHotkeys;
        private readonly Form parentForm;
        private int nextHotkeyId = 1;
        private bool disposed = false;

        public delegate Task HotkeyAction();

        public HotkeyManager(Form parent)
        {
            parentForm = parent ?? throw new ArgumentNullException(nameof(parent));
            registeredHotkeys = new Dictionary<int, HotkeyAction>();
            
            // Hook into the parent form's WndProc
            if (parentForm.Handle != IntPtr.Zero)
            {
                SetupMessageFilter();
            }
            else
            {
                parentForm.HandleCreated += (s, e) => SetupMessageFilter();
            }
        }

        private void SetupMessageFilter()
        {
            // Add a message filter to capture hotkey messages
            Application.AddMessageFilter(new HotkeyMessageFilter(this));
        }

        /// <summary>
        /// Register a global hotkey
        /// </summary>
        public int RegisterHotkey(Keys key, ModifierKeys modifiers, HotkeyAction action)
        {
            if (disposed) throw new ObjectDisposedException(nameof(HotkeyManager));

            int hotkeyId = nextHotkeyId++;
            int modifierFlags = ConvertModifiers(modifiers);
            int virtualKey = (int)key;

            bool success = RegisterHotKey(parentForm.Handle, hotkeyId, modifierFlags, virtualKey);
            
            if (success)
            {
                registeredHotkeys[hotkeyId] = action;
                return hotkeyId;
            }

            throw new InvalidOperationException($"Failed to register hotkey: {modifiers}+{key}");
        }

        /// <summary>
        /// Register a hotkey from string representation (e.g., "Ctrl+F5", "Alt+Shift+J")
        /// </summary>
        public int RegisterHotkey(string hotkeyString, HotkeyAction action)
        {
            ParseHotkeyString(hotkeyString, out Keys key, out ModifierKeys modifiers);
            return RegisterHotkey(key, modifiers, action);
        }

        /// <summary>
        /// Unregister a hotkey
        /// </summary>
        public bool UnregisterHotkey(int hotkeyId)
        {
            if (disposed) return false;

            if (registeredHotkeys.ContainsKey(hotkeyId))
            {
                bool success = UnregisterHotKey(parentForm.Handle, hotkeyId);
                if (success)
                {
                    registeredHotkeys.Remove(hotkeyId);
                }
                return success;
            }

            return false;
        }

        /// <summary>
        /// Check if a key is currently pressed (for manual polling)
        /// </summary>
        public static bool IsKeyPressed(Keys key)
        {
            return (GetAsyncKeyState((int)key) & 0x8000) != 0;
        }

        /// <summary>
        /// Handle hotkey message
        /// </summary>
        internal async void HandleHotkeyMessage(int hotkeyId)
        {
            if (registeredHotkeys.TryGetValue(hotkeyId, out HotkeyAction action))
            {
                try
                {
                    await action();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[HotkeyManager] Error executing hotkey action: {ex.Message}");
                }
            }
        }

        private int ConvertModifiers(ModifierKeys modifiers)
        {
            int flags = 0;
            if (modifiers.HasFlag(ModifierKeys.Alt)) flags |= MOD_ALT;
            if (modifiers.HasFlag(ModifierKeys.Control)) flags |= MOD_CONTROL;
            if (modifiers.HasFlag(ModifierKeys.Shift)) flags |= MOD_SHIFT;
            if (modifiers.HasFlag(ModifierKeys.Windows)) flags |= MOD_WIN;
            return flags;
        }

        private void ParseHotkeyString(string hotkeyString, out Keys key, out ModifierKeys modifiers)
        {
            modifiers = ModifierKeys.None;
            key = Keys.None;

            if (string.IsNullOrEmpty(hotkeyString))
                throw new ArgumentException("Hotkey string cannot be null or empty");

            string[] parts = hotkeyString.Split('+');
            
            for (int i = 0; i < parts.Length - 1; i++)
            {
                string modifier = parts[i].Trim().ToLower();
                switch (modifier)
                {
                    case "ctrl":
                    case "control":
                        modifiers |= ModifierKeys.Control;
                        break;
                    case "alt":
                        modifiers |= ModifierKeys.Alt;
                        break;
                    case "shift":
                        modifiers |= ModifierKeys.Shift;
                        break;
                    case "win":
                    case "windows":
                        modifiers |= ModifierKeys.Windows;
                        break;
                }
            }

            // Parse the main key
            string keyString = parts[parts.Length - 1].Trim();
            if (Enum.TryParse(keyString, true, out Keys parsedKey))
            {
                key = parsedKey;
            }
            else
            {
                throw new ArgumentException($"Invalid key: {keyString}");
            }
        }

        public void Dispose()
        {
            if (!disposed)
            {
                // Unregister all hotkeys
                foreach (int hotkeyId in registeredHotkeys.Keys)
                {
                    UnregisterHotKey(parentForm.Handle, hotkeyId);
                }
                registeredHotkeys.Clear();
                disposed = true;
            }
        }
    }

    /// <summary>
    /// Modifier keys for hotkeys
    /// </summary>
    [Flags]
    public enum ModifierKeys
    {
        None = 0,
        Alt = 1,
        Control = 2,
        Shift = 4,
        Windows = 8
    }

    /// <summary>
    /// Message filter to capture hotkey messages
    /// </summary>
    internal class HotkeyMessageFilter : IMessageFilter
    {
        private const int WM_HOTKEY = 0x0312;
        private readonly HotkeyManager hotkeyManager;

        public HotkeyMessageFilter(HotkeyManager manager)
        {
            hotkeyManager = manager;
        }

        public bool PreFilterMessage(ref Message m)
        {
            if (m.Msg == WM_HOTKEY)
            {
                int hotkeyId = m.WParam.ToInt32();
                hotkeyManager.HandleHotkeyMessage(hotkeyId);
                return true;
            }
            return false;
        }
    }

    /// <summary>
    /// Predefined hotkey combinations for common features
    /// </summary>
    public static class CommonHotkeys
    {
        public const string SuperJump = "F5";
        public const string SuperJumpToggle = "Ctrl+F5";
        public const string SuperJumpIncrease = "Ctrl+Shift+Plus";
        public const string SuperJumpDecrease = "Ctrl+Shift+Minus";
        public const string RestoreNormal = "Ctrl+R";
        public const string EmergencyDisable = "Ctrl+Alt+X";
    }
}
