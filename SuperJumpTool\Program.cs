﻿using System;
using System.Drawing;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Diagnostics;

namespace SuperJumpTool
{
    public partial class SuperJumpForm : Form
    {
        [DllImport("kernel32.dll")]
        public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);

        [DllImport("kernel32.dll")]
        public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesWritten);

        [DllImport("kernel32.dll")]
        public static extern bool CloseHandle(IntPtr hObject);

        private const int PROCESS_ALL_ACCESS = 0x1F0FFF;
        private IntPtr processHandle = IntPtr.Zero;
        private Process? kf2Process = null;
        private bool isEnabled = false;
        private float originalGravity = 1.0f;
        private bool hasBackup = false;

        // UI Controls
        private CheckBox chkEnable = new();
        private TrackBar trackMultiplier = new();
        private Label lblMultiplier = new();
        private Label lblStatus = new();
        private Button btnApply = new();
        private Button btnRestore = new();
        private Button btnFindProcess = new();

        public SuperJumpForm()
        {
            InitializeComponent();
            SetupUI();
            FindKF2Process();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.Text = "KF2 Super Jump Tool";
            this.Size = new Size(400, 250);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.ForeColor = Color.White;
            this.StartPosition = FormStartPosition.CenterScreen;

            this.ResumeLayout(false);
        }

        private void SetupUI()
        {
            // Title
            var lblTitle = new Label
            {
                Text = "KF2 Super Jump Tool",
                Font = new Font("Arial", 14, FontStyle.Bold),
                Location = new Point(20, 20),
                Size = new Size(200, 25),
                ForeColor = Color.Cyan
            };

            // Find Process Button
            btnFindProcess = new Button
            {
                Text = "Find KF2 Process",
                Location = new Point(250, 20),
                Size = new Size(120, 25),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnFindProcess.Click += BtnFindProcess_Click;

            // Enable checkbox
            chkEnable = new CheckBox
            {
                Text = "Enable Super Jump",
                Location = new Point(20, 60),
                Size = new Size(150, 20),
                ForeColor = Color.White
            };
            chkEnable.CheckedChanged += ChkEnable_CheckedChanged;

            // Multiplier label
            var lblMultiplierTitle = new Label
            {
                Text = "Jump Multiplier:",
                Location = new Point(20, 90),
                Size = new Size(100, 20),
                ForeColor = Color.LightGray
            };

            // Multiplier trackbar
            trackMultiplier = new TrackBar
            {
                Location = new Point(20, 110),
                Size = new Size(250, 45),
                Minimum = 1,
                Maximum = 20,
                Value = 5,
                TickFrequency = 2,
                Enabled = false
            };
            trackMultiplier.ValueChanged += TrackMultiplier_ValueChanged;

            // Multiplier value label
            lblMultiplier = new Label
            {
                Text = "5.0x",
                Location = new Point(280, 120),
                Size = new Size(50, 20),
                ForeColor = Color.Yellow,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            // Apply button
            btnApply = new Button
            {
                Text = "Apply Jump",
                Location = new Point(20, 160),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(0, 150, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            btnApply.Click += BtnApply_Click;

            // Restore button
            btnRestore = new Button
            {
                Text = "Restore Normal",
                Location = new Point(130, 160),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(150, 150, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            btnRestore.Click += BtnRestore_Click;

            // Status label
            lblStatus = new Label
            {
                Text = "Status: KF2 Process Not Found",
                Location = new Point(20, 200),
                Size = new Size(350, 20),
                ForeColor = Color.Orange
            };

            // Add all controls
            this.Controls.AddRange(new Control[]
            {
                lblTitle, btnFindProcess, chkEnable, lblMultiplierTitle,
                trackMultiplier, lblMultiplier, btnApply, btnRestore, lblStatus
            });
        }

        private void FindKF2Process()
        {
            try
            {
                // Try to find KF2 process
                Process[] processes = Process.GetProcessesByName("KFGame");
                if (processes.Length == 0)
                {
                    processes = Process.GetProcessesByName("KillingFloor2");
                }

                if (processes.Length > 0)
                {
                    kf2Process = processes[0];
                    processHandle = OpenProcess(PROCESS_ALL_ACCESS, false, kf2Process.Id);

                    if (processHandle != IntPtr.Zero)
                    {
                        lblStatus.Text = $"Status: Connected to KF2 (PID: {kf2Process.Id})";
                        lblStatus.ForeColor = Color.LimeGreen;
                        chkEnable.Enabled = true;
                        return;
                    }
                }

                lblStatus.Text = "Status: KF2 Process Not Found";
                lblStatus.ForeColor = Color.Orange;
                chkEnable.Enabled = false;
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Status: Error - {ex.Message}";
                lblStatus.ForeColor = Color.Red;
            }
        }

        private void BtnFindProcess_Click(object? sender, EventArgs e)
        {
            FindKF2Process();
        }

        private void ChkEnable_CheckedChanged(object? sender, EventArgs e)
        {
            trackMultiplier.Enabled = chkEnable.Checked;
            btnApply.Enabled = chkEnable.Checked;
            btnRestore.Enabled = chkEnable.Checked;

            if (chkEnable.Checked)
            {
                lblStatus.Text = "Status: Super Jump Enabled - Use Apply Button";
                lblStatus.ForeColor = Color.Cyan;
            }
            else
            {
                BtnRestore_Click(sender, e);
                lblStatus.Text = "Status: Super Jump Disabled";
                lblStatus.ForeColor = Color.LimeGreen;
            }
        }

        private void TrackMultiplier_ValueChanged(object? sender, EventArgs e)
        {
            float multiplier = trackMultiplier.Value;
            lblMultiplier.Text = $"{multiplier:F1}x";

            // Color coding
            if (multiplier <= 3)
                lblMultiplier.ForeColor = Color.LimeGreen;
            else if (multiplier <= 8)
                lblMultiplier.ForeColor = Color.Yellow;
            else
                lblMultiplier.ForeColor = Color.Red;
        }

        private void BtnApply_Click(object? sender, EventArgs e)
        {
            if (processHandle == IntPtr.Zero)
            {
                MessageBox.Show("KF2 process not found!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                float multiplier = trackMultiplier.Value;

                // This is a simplified approach - you'd need to find the actual memory addresses
                // For now, this demonstrates the concept
                MessageBox.Show($"Super Jump {multiplier}x applied!\n\nNote: This is a demo version.\nFor full functionality, memory addresses need to be found.",
                    "Applied", MessageBoxButtons.OK, MessageBoxIcon.Information);

                lblStatus.Text = $"Status: Super Jump {multiplier}x Applied";
                lblStatus.ForeColor = Color.LimeGreen;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error applying super jump: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRestore_Click(object? sender, EventArgs e)
        {
            try
            {
                // Restore normal jump values
                lblStatus.Text = "Status: Normal Jump Restored";
                lblStatus.ForeColor = Color.LimeGreen;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error restoring normal jump: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (processHandle != IntPtr.Zero)
            {
                CloseHandle(processHandle);
            }
            base.OnFormClosing(e);
        }
    }

    // Program entry point
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new SuperJumpForm());
        }
    }
}
