<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Memory</name>
    </assembly>
    <members>
        <member name="T:Memory.Mem">
            <summary>
            Memory.dll class. Full documentation at https://github.com/erfg12/memory.dll/wiki
            </summary>
        </member>
        <member name="M:Memory.Mem.OpenProcess(System.Int32,System.String@)">
            <summary>
            Open the PC game process with all security and access rights.
            </summary>
            <param name="pid">Use process name or process ID here.</param>
            <returns>Process opened successfully or failed.</returns>
            <param name="FailReason">Show reason open process fails</param>
        </member>
        <member name="M:Memory.Mem.OpenProcess(System.String,System.String@)">
            <summary>
            Open the PC game process with all security and access rights.
            </summary>
            <param name="proc">Use process name or process ID here.</param>
            <param name="FailReason">Show reason open process fails</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.OpenProcess(System.String)">
            <summary>
            Open the PC game process with all security and access rights.
            </summary>
            <param name="proc">Use process name or process ID here.</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.OpenProcess(System.Int32)">
            <summary>
            Open the PC game process with all security and access rights.
            </summary>
            <param name="pid">Use process name or process ID here.</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.SetFocus">
            <summary>
            Builds the process modules dictionary (names with addresses). Use mProc.Process.Modules instead.
            </summary>
        </member>
        <member name="M:Memory.Mem.GetProcIdFromName(System.String)">
            <summary>
            Get the process ID number by process name.
            </summary>
            <param name="name">Example: "eqgame". Use task manager to find the name. Do not include .exe</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.LoadCode(System.String,System.String)">
            <summary>
            Get code. If just the ini file name is given with no path, it will assume the file is next to the executable.
            </summary>
            <param name="name">label for address or code</param>
            <param name="iniFile">path and name of ini file</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ThreadStartClient(System.String,System.String)">
            <summary>
            Make a named pipe (if not already made) and call to a remote function.
            </summary>
            <param name="func">remote function to call</param>
            <param name="name">name of the thread</param>
        </member>
        <member name="M:Memory.Mem.GetCode(System.String,System.String,System.Int32)">
            <summary>
            Convert code from string to real address. If path is not blank, will pull from ini file.
            </summary>
            <param name="name">label in ini file or code</param>
            <param name="path">path to ini file (OPTIONAL)</param>
            <param name="size">size of address (default is 8)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.GetModuleAddressByName(System.String)">
            <summary>
            Retrieve mProc.Process module baseaddress by name
            </summary>
            <param name="name">name of module</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.Get64BitCode(System.String,System.String,System.Int32)">
            <summary>
            Convert code from string to real address. If path is not blank, will pull from ini file.
            </summary>
            <param name="name">label in ini file OR code</param>
            <param name="path">path to ini file (OPTIONAL)</param>
            <param name="size">size of address (default is 16)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.CloseProcess">
            <summary>
            Close the process when finished.
            </summary>
        </member>
        <member name="M:Memory.Mem.InjectDll(System.String)">
            <summary>
            Inject a DLL file.
            </summary>
            <param name="strDllName">path and name of DLL file. Ex: "C:\MyTrainer\inject.dll" or "inject.dll" if the DLL file is in the same directory as the trainer.</param>
        </member>
        <member name="M:Memory.Mem.CreateCodeCave(System.String,System.Byte[],System.Int32,System.Int32,System.String)">
            <summary>
            Creates a code cave to write custom opcodes in target process
            </summary>
            <param name="code">Address to create the trampoline</param>
            <param name="newBytes">The opcodes to write in the code cave</param>
            <param name="replaceCount">The number of bytes being replaced</param>
            <param name="size">size of the allocated region</param>
            <param name="file">ini file to look in</param>
            <remarks>Please ensure that you use the proper replaceCount
            if you replace halfway in an instruction you may cause bad things</remarks>
            <returns>UIntPtr to created code cave for use for later deallocation</returns>
        </member>
        <member name="M:Memory.Mem.ByteArrayToHexString(System.Byte[])">
            <summary>
            Convert a byte array to hex values in a string.
            </summary>
            <param name="ba">your byte array to convert</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.DumpMemory(System.String)">
            <summary>
            Dump memory page by page to a dump.dmp file. Can be used with Cheat Engine.
            </summary>
        </member>
        <member name="M:Memory.Mem.GetThreads">
            <summary>
            get a list of available threads in opened process
            </summary>
        </member>
        <member name="M:Memory.Mem.GetThreadStartAddress(System.Int32)">
            <summary>
            Get thread base address by ID. Provided by github.com/osadrac
            </summary>
            <param name="threadId"></param>
            <returns></returns>
            <exception cref="T:System.ComponentModel.Win32Exception"></exception>
        </member>
        <member name="M:Memory.Mem.SuspendThreadByID(System.Int32)">
            <summary>
            suspend a thread by ID
            </summary>
            <param name="ThreadID">the thread you wish to suspend by ID</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.AoBScan(System.String,System.Boolean,System.Boolean,System.String)">
            <summary>
            Array of byte scan.
            </summary>
            <param name="search">array of bytes to search for, OR your ini code label.</param>
            <param name="writable">Include writable addresses in scan</param>
            <param name="executable">Include executable addresses in scan</param>
            <param name="file">ini file (OPTIONAL)</param>
            <returns>IEnumerable of all addresses found.</returns>
        </member>
        <member name="M:Memory.Mem.AoBScan(System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Array of byte scan.
            </summary>
            <param name="search">array of bytes to search for, OR your ini code label.</param>
            <param name="readable">Include readable addresses in scan</param>
            <param name="writable">Include writable addresses in scan</param>
            <param name="executable">Include executable addresses in scan</param>
            <param name="file">ini file (OPTIONAL)</param>
            <returns>IEnumerable of all addresses found.</returns>
        </member>
        <member name="M:Memory.Mem.AoBScan(System.Int64,System.Int64,System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Array of Byte scan.
            </summary>
            <param name="start">Your starting address.</param>
            <param name="end">ending address</param>
            <param name="search">array of bytes to search for, OR your ini code label.</param>
            <param name="file">ini file (OPTIONAL)</param>
            <param name="writable">Include writable addresses in scan</param>
            <param name="executable">Include executable addresses in scan</param>
            <param name="mapped">Include mapped addresses in scan</param>
            <returns>IEnumerable of all addresses found.</returns>
        </member>
        <member name="M:Memory.Mem.AoBScan(System.Int64,System.Int64,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Array of Byte scan.
            </summary>
            <param name="start">Your starting address.</param>
            <param name="end">ending address</param>
            <param name="search">array of bytes to search for, OR your ini code label.</param>
            <param name="file">ini file (OPTIONAL)</param>
            <param name="readable">Include readable addresses in scan</param>
            <param name="writable">Include writable addresses in scan</param>
            <param name="executable">Include executable addresses in scan</param>
            <param name="mapped">Include mapped addresses in scan</param>
            <returns>IEnumerable of all addresses found.</returns>
        </member>
        <member name="M:Memory.Mem.AoBScan(System.String,System.Int64,System.String,System.String)">
            <summary>
            Array of bytes scan
            </summary>
            <param name="code">Starting address or ini label</param>
            <param name="end">ending address</param>
            <param name="search">array of bytes to search for or your ini code label</param>
            <param name="file">ini file</param>
            <returns>First address found</returns>
        </member>
        <member name="M:Memory.Mem.CutString(System.String)">
            <summary>
            Cut a string that goes on for too long or one that is possibly merged with another string.
            </summary>
            <param name="str">The string you want to cut.</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadBytes(System.String,System.Int64,System.String)">
            <summary>
            Reads up to `length ` bytes from an address.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="length">The maximum bytes to read.</param>
            <param name="file">path and name of ini file.</param>
            <returns>The bytes read or null</returns>
        </member>
        <member name="M:Memory.Mem.ReadFloat(System.String,System.String,System.Boolean)">
            <summary>
            Read a float value from an address.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="file">path and name of ini file. (OPTIONAL)</param>
            <param name="round">Round the value to 2 decimal places</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadString(System.String,System.String,System.Int32,System.Boolean,System.Text.Encoding)">
            <summary>
            Read a string value from an address.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="file">path and name of ini file. (OPTIONAL)</param>
            <param name="length">length of bytes to read (OPTIONAL)</param>
            <param name="zeroTerminated">terminate string at null char</param>
            <param name="stringEncoding">System.Text.Encoding.UTF8 (DEFAULT). Other options: ascii, unicode, utf32, utf7</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadDouble(System.String,System.String,System.Boolean)">
            <summary>
            Read a double value
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="file">path and name of ini file. (OPTIONAL)</param>
            <param name="round">Round the value to 2 decimal places</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadInt(System.String,System.String)">
            <summary>
            Read an integer from an address.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="file">path and name of ini file. (OPTIONAL)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadLong(System.String,System.String)">
            <summary>
            Read a long value from an address.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="file">path and name of ini file. (OPTIONAL)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadUInt(System.String,System.String)">
            <summary>
            Read a UInt value from address.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="file">path and name of ini file. (OPTIONAL)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.Read2ByteMove(System.String,System.Int32,System.String)">
            <summary>
            Reads a 2 byte value from an address and moves the address.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="moveQty">Quantity to move.</param>
            <param name="file">path and name of ini file (OPTIONAL)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadIntMove(System.String,System.Int32,System.String)">
            <summary>
            Reads an integer value from address and moves the address.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="moveQty">Quantity to move.</param>
            <param name="file">path and name of ini file (OPTIONAL)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadUIntMove(System.String,System.Int32,System.String)">
            <summary>
            Get UInt and move to another address by moveQty. Use in a for loop.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="moveQty">Quantity to move.</param>
            <param name="file">path and name of ini file (OPTIONAL)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.Read2Byte(System.String,System.String)">
            <summary>
            Read a 2 byte value from an address. Returns an integer.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="file">path and file name to ini file. (OPTIONAL)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadByte(System.String,System.String)">
            <summary>
            Read 1 byte from address.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="file">path and file name of ini file. (OPTIONAL)</param>
            <returns></returns>
        </member>
        <member name="M:Memory.Mem.ReadBits(System.String,System.String)">
            <summary>
            Reads a byte from memory and splits it into bits
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="file">path and file name of ini file. (OPTIONAL)</param>
            <returns>Array of 8 booleans representing each bit of the byte read</returns>
        </member>
        <member name="M:Memory.Mem.BindToUI``1(System.String,System.Action{System.String},System.String)">
            <summary>
            Reads a memory address, keeps value in UI object. Ex: BindToUI("0x12345678,0x02,0x05", v => ObjName.Invoke((MethodInvoker)delegate { if (String.Compare(v, ObjName.Text) != 0) { ObjName.Text = v; } }));
            </summary>
            <param name="address">Your code or INI file variable name</param>
            <param name="UIObject">Returning variable to bind to UI object. See example in summary.</param>
            <param name="file">OPTIONAL: INI file path and file name with extension</param>
        </member>
        <member name="M:Memory.Mem.FreezeValue(System.String,System.String,System.String,System.String)">
            <summary>
            Freeze a value to an address.
            </summary>
            <param name="address">Your address</param>
            <param name="type">byte, 2bytes, bytes, float, int, string, double or long.</param>
            <param name="value">Value to freeze</param>
            <param name="file">ini file to read address from (OPTIONAL)</param>
        </member>
        <member name="M:Memory.Mem.UnfreezeValue(System.String)">
            <summary>
            Unfreeze a frozen value at an address
            </summary>
            <param name="address">address where frozen value is stored</param>
        </member>
        <member name="M:Memory.Mem.WriteMemory(System.String,System.String,System.String,System.String,System.Text.Encoding,System.Boolean)">
            <summary>
            Write to memory address. See https://github.com/erfg12/memory.dll/wiki/writeMemory() for more information.
            </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="type">byte, 2bytes, bytes, float, int, string, double or long.</param>
            <param name="write">value to write to address.</param>
            <param name="file">path and name of .ini file (OPTIONAL)</param>
            <param name="stringEncoding">System.Text.Encoding.UTF8 (DEFAULT). Other options: ascii, unicode, utf32, utf7</param>
            <param name="RemoveWriteProtection">If building a trainer on an emulator (Ex: RPCS3) you'll want to set this to false</param>
        </member>
        <member name="M:Memory.Mem.WriteMove(System.String,System.String,System.String,System.Int32,System.String,System.Int32)">
             <summary>
             Write to address and move by moveQty. Good for byte arrays. See https://github.com/erfg12/memory.dll/wiki/Writing-a-Byte-Array for more information.
             </summary>
            <param name="code">address, module + pointer + offset, module + offset OR label in .ini file.</param>
            <param name="type">byte, bytes, float, int, string or long.</param>
             <param name="write">byte to write</param>
             <param name="MoveQty">quantity to move</param>
             <param name="file">path and name of .ini file (OPTIONAL)</param>
             <param name="SlowDown">milliseconds to sleep between each byte</param>
             <returns></returns>
        </member>
        <member name="M:Memory.Mem.WriteBytes(System.String,System.Byte[],System.String)">
            <summary>
            Write byte array to addresses.
            </summary>
            <param name="code">address to write to</param>
            <param name="write">byte array to write</param>
            <param name="file">path and name of ini file. (OPTIONAL)</param>
        </member>
        <member name="M:Memory.Mem.WriteBits(System.String,System.Boolean[],System.String)">
            <summary>
            Takes an array of 8 booleans and writes to a single byte
            </summary>
            <param name="code">address to write to</param>
            <param name="bits">Array of 8 booleans to write</param>
            <param name="file">path and name of ini file. (OPTIONAL)</param>
        </member>
        <member name="M:Memory.Mem.WriteBytes(System.UIntPtr,System.Byte[])">
            <summary>
            Write byte array to address
            </summary>
            <param name="address">Address to write to</param>
            <param name="write">Byte array to write to</param>
        </member>
        <member name="T:Memory.MemoryRegionResult">
            <summary>
            AoB scan information.
            </summary>
        </member>
        <member name="T:Memory.Proc">
            <summary>
            Information about the opened process.
            </summary>
        </member>
    </members>
</doc>
