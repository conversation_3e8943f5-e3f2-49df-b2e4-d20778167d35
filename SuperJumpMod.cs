using System;
using System.Windows.Forms;
using Memory;

namespace KillingFloor2_External
{
    /// <summary>
    /// Enhanced Super Jump modification for the existing KF2 tool
    /// This can be integrated into your current form
    /// </summary>
    public class SuperJumpMod
    {
        private Mem memory;
        private bool isEnabled = false;
        private float jumpMultiplier = 1.0f;
        private IntPtr playerBaseAddress = IntPtr.Zero;
        
        // Memory offsets (you may need to adjust these for your game version)
        private const int JUMP_FORCE_OFFSET = 0x2A4;
        private const int GRAVITY_OFFSET = 0x2A8;
        private const float DEFAULT_JUMP_FORCE = 420.0f;
        private const float DEFAULT_GRAVITY = 1.0f;

        public SuperJumpMod(Mem memoryInstance)
        {
            memory = memoryInstance;
        }

        /// <summary>
        /// Enable super jump with specified multiplier
        /// </summary>
        public bool EnableSuperJump(float multiplier)
        {
            try
            {
                jumpMultiplier = Math.Max(0.1f, Math.Min(20.0f, multiplier)); // Clamp between 0.1x and 20x
                
                // Find player base address (you'll need to implement this based on your current tool's method)
                playerBaseAddress = FindPlayerBaseAddress();
                if (playerBaseAddress == IntPtr.Zero)
                {
                    return false;
                }

                // Calculate enhanced values
                float enhancedJumpForce = DEFAULT_JUMP_FORCE * jumpMultiplier;
                float reducedGravity = DEFAULT_GRAVITY / (jumpMultiplier * 0.5f);

                // Write to memory
                memory.WriteMemory($"0x{playerBaseAddress.ToInt64() + JUMP_FORCE_OFFSET:X}", "float", enhancedJumpForce.ToString());
                memory.WriteMemory($"0x{playerBaseAddress.ToInt64() + GRAVITY_OFFSET:X}", "float", reducedGravity.ToString());

                isEnabled = true;
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SuperJump Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disable super jump and restore normal values
        /// </summary>
        public bool DisableSuperJump()
        {
            try
            {
                if (playerBaseAddress == IntPtr.Zero) return false;

                // Restore normal values
                memory.WriteMemory($"0x{playerBaseAddress.ToInt64() + JUMP_FORCE_OFFSET:X}", "float", DEFAULT_JUMP_FORCE.ToString());
                memory.WriteMemory($"0x{playerBaseAddress.ToInt64() + GRAVITY_OFFSET:X}", "float", DEFAULT_GRAVITY.ToString());

                isEnabled = false;
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SuperJump Disable Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Find player base address - you'll need to implement this based on your current tool's method
        /// This is a placeholder that you should replace with your existing player finding logic
        /// </summary>
        private IntPtr FindPlayerBaseAddress()
        {
            try
            {
                // Use your existing method from the current tool to find the player base
                // This might be something like:
                // return YourExistingPlayerFinder.GetPlayerBase();
                
                // For now, return a placeholder - you need to implement this
                // based on how your current tool finds the player
                return IntPtr.Zero;
            }
            catch
            {
                return IntPtr.Zero;
            }
        }

        public bool IsEnabled => isEnabled;
        public float CurrentMultiplier => jumpMultiplier;
    }

    /// <summary>
    /// Extension to add Super Jump controls to your existing form
    /// Add this to your main form class
    /// </summary>
    public static class SuperJumpFormExtension
    {
        /// <summary>
        /// Add Super Jump controls to your existing form
        /// Call this in your form's constructor or Load event
        /// </summary>
        public static void AddSuperJumpControls(this Form form, Mem memory)
        {
            var superJump = new SuperJumpMod(memory);

            // Create Super Jump checkbox
            var chkSuperJump = new CheckBox
            {
                Text = "Super Jump",
                Location = new System.Drawing.Point(20, 220), // Adjust position as needed
                Size = new System.Drawing.Size(100, 20),
                ForeColor = System.Drawing.Color.White
            };

            // Create multiplier trackbar
            var trackMultiplier = new TrackBar
            {
                Location = new System.Drawing.Point(130, 215),
                Size = new System.Drawing.Size(150, 30),
                Minimum = 1,
                Maximum = 20,
                Value = 5,
                TickFrequency = 2
            };

            // Create multiplier label
            var lblMultiplier = new Label
            {
                Text = "5.0x",
                Location = new System.Drawing.Point(290, 220),
                Size = new System.Drawing.Size(40, 20),
                ForeColor = System.Drawing.Color.Cyan
            };

            // Event handlers
            chkSuperJump.CheckedChanged += (s, e) =>
            {
                if (chkSuperJump.Checked)
                {
                    float multiplier = trackMultiplier.Value;
                    if (superJump.EnableSuperJump(multiplier))
                    {
                        lblMultiplier.ForeColor = System.Drawing.Color.LimeGreen;
                    }
                    else
                    {
                        chkSuperJump.Checked = false;
                        MessageBox.Show("Failed to enable Super Jump. Make sure KF2 is running.", "Error");
                    }
                }
                else
                {
                    superJump.DisableSuperJump();
                    lblMultiplier.ForeColor = System.Drawing.Color.Cyan;
                }
            };

            trackMultiplier.ValueChanged += (s, e) =>
            {
                float multiplier = trackMultiplier.Value;
                lblMultiplier.Text = $"{multiplier:F1}x";
                
                // Update color based on intensity
                if (multiplier <= 3)
                    lblMultiplier.ForeColor = System.Drawing.Color.LimeGreen;
                else if (multiplier <= 8)
                    lblMultiplier.ForeColor = System.Drawing.Color.Yellow;
                else
                    lblMultiplier.ForeColor = System.Drawing.Color.Red;

                // If currently enabled, update the jump multiplier
                if (chkSuperJump.Checked)
                {
                    superJump.EnableSuperJump(multiplier);
                }
            };

            // Add controls to form
            form.Controls.Add(chkSuperJump);
            form.Controls.Add(trackMultiplier);
            form.Controls.Add(lblMultiplier);
        }
    }
}

// Usage example for your main form:
/*
public partial class MainForm : Form
{
    private Mem memory;
    
    public MainForm()
    {
        InitializeComponent();
        memory = new Mem();
        
        // Add Super Jump controls
        this.AddSuperJumpControls(memory);
    }
}
*/
